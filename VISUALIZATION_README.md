# Universal Anomaly Detection Visualization Tool

This tool provides a universal way to generate Figure 9 style visualizations for anomaly detection results across different datasets.

## Features

- **Universal Dataset Support**: Works with SWaT, MSL, SMAP, SMD, PSM, and custom datasets (like TEST)
- **Automatic Data Loading**: Automatically detects and loads data based on dataset type
- **Figure 9 Style Visualization**: Generates publication-ready visualizations similar to Figure 9 in the Anomaly Transformer paper
- **Flexible Input**: Can work with prediction files, energy scores, or integrate directly with the main solver

## Files

1. **`anomaly_visualization.py`** - Main universal visualization tool
2. **`run_visualization_examples.py`** - Example script showing usage for different datasets
3. **Modified `solver.py`** - Updated solver with integrated universal visualization

## Usage

### Method 1: Standalone Visualization Tool

```bash
# For SWaT dataset
python anomaly_visualization.py --dataset SWaT --data_path dataset/SWaT --input_c 51 --pred_file pred.csv

# For your custom TEST dataset
python anomaly_visualization.py --dataset TEST --data_path dataset/TEST --input_c 10 --pred_file pred.csv

# For MSL dataset
python anomaly_visualization.py --dataset MSL --data_path dataset/MSL --input_c 55 --pred_file pred.csv
```

### Method 2: Using Example Script

```bash
# Run all available dataset examples
python run_visualization_examples.py

# Interactive mode for single dataset
python run_visualization_examples.py --interactive
```

### Method 3: Integrated with Main Training/Testing

The visualization is now automatically generated when you run the main script:

```bash
# SWaT dataset
python main.py --anormly_ratio 0.1 --num_epochs 10 --batch_size 64 --mode test --dataset SWaT --data_path dataset/SWaT --input_c 51 --output_c 51 --pretrained_model 10

# Your TEST dataset
python main.py --anormly_ratio 1 --num_epochs 10 --batch_size 64 --mode test --dataset TEST --data_path dataset/TEST --input_c 10 --output_c 10 --pretrained_model 20
```

## Parameters

- `--dataset`: Dataset name (SWaT, MSL, SMAP, SMD, PSM, TEST, or custom)
- `--data_path`: Path to the dataset directory
- `--input_c`: Number of input channels/features
- `--pred_file`: Path to predictions CSV file (default: pred.csv)
- `--energy_file`: Path to energy scores CSV file (optional)

## Dataset Structure Requirements

The tool expects the following file structure for different datasets:

### For .npy datasets (SWaT, MSL, SMAP, SMD):
```
dataset/DATASET_NAME/
├── dataset_name_test.npy
└── dataset_name_test_label.npy
```

### For .csv datasets (PSM, TEST, custom):
```
dataset/DATASET_NAME/
├── test.csv
└── test_label.csv
```

## Output

The tool generates:
1. **PNG file**: `{dataset}_anomaly_detection_figure9.png` (high-resolution)
2. **PDF file**: `{dataset}_anomaly_detection_figure9.pdf` (vector format)

Each visualization contains:
- **Upper subplot**: Input time series with anomaly regions highlighted
- **Lower subplot**: Association-based criterion with threshold line and detected anomalies
- **Annotations**: "Early Stage Detection" markers where applicable

## Customization

### Adding New Datasets

To add support for a new dataset, modify the `_load_test_data_for_visualization` method in `solver.py` or the `load_test_data` method in `anomaly_visualization.py`:

```python
elif self.dataset == 'YOUR_DATASET':
    test_data = np.load(self.data_path + "/your_test_file.npy")
    test_labels = np.load(self.data_path + "/your_label_file.npy")
```

### Customizing Visualization

You can modify the visualization by:
- Changing colors in the plotting functions
- Adjusting the segment length for visualization
- Modifying annotation styles
- Adding additional metrics or information

## Examples

### Example 1: SWaT Dataset
```bash
python anomaly_visualization.py --dataset SWaT --data_path dataset/SWaT --input_c 51
```

### Example 2: Custom TEST Dataset
```bash
python anomaly_visualization.py --dataset TEST --data_path dataset/TEST --input_c 10
```

### Example 3: With Custom Energy File
```bash
python anomaly_visualization.py --dataset TEST --data_path dataset/TEST --input_c 10 --energy_file energy_scores.csv
```

## Troubleshooting

1. **File Not Found Errors**: Ensure your dataset files are in the correct location and format
2. **Shape Mismatch**: Check that your input_c parameter matches the actual number of features in your data
3. **Missing Dependencies**: Install required packages: `pip install matplotlib pandas numpy scikit-learn`

## Integration with Existing Code

The modified `solver.py` automatically calls the visualization function during testing. The visualization will be generated after the model evaluation is complete.

## Notes

- The tool automatically handles different data formats (.npy and .csv)
- If original test data cannot be loaded, synthetic data is used for demonstration
- The visualization focuses on segments containing anomalies for better demonstration
- All generated files are saved in the current working directory
