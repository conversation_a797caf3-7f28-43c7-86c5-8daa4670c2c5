#!/usr/bin/env python3
"""
Example script showing how to use the universal anomaly visualization tool
for different datasets including SWaT and custom TEST dataset.
"""

import os
import subprocess
import sys

def run_visualization(dataset, data_path, input_c, pred_file='pred.csv', energy_file=None):
    """
    Run visualization for a specific dataset
    """
    print(f"\n{'='*60}")
    print(f"Running visualization for {dataset} dataset")
    print(f"{'='*60}")
    
    cmd = [
        'python', 'anomaly_visualization.py',
        '--dataset', dataset,
        '--data_path', data_path,
        '--input_c', str(input_c),
        '--pred_file', pred_file
    ]
    
    if energy_file:
        cmd.extend(['--energy_file', energy_file])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("Visualization completed successfully!")
            print(result.stdout)
        else:
            print("Error occurred:")
            print(result.stderr)
    except Exception as e:
        print(f"Failed to run visualization: {e}")

def main():
    """
    Main function with examples for different datasets
    """
    print("Universal Anomaly Detection Visualization Examples")
    print("=" * 60)
    
    # Example 1: SWaT dataset
    print("\nExample 1: SWaT Dataset")
    print("-" * 30)
    swat_config = {
        'dataset': 'SWaT',
        'data_path': 'dataset/SWaT',
        'input_c': 51,
        'pred_file': 'pred.csv'
    }
    
    if os.path.exists(swat_config['data_path']):
        run_visualization(**swat_config)
    else:
        print(f"SWaT dataset not found at {swat_config['data_path']}")
    
    # Example 2: Custom TEST dataset
    print("\nExample 2: Custom TEST Dataset")
    print("-" * 30)
    test_config = {
        'dataset': 'TEST',
        'data_path': 'dataset/TEST',
        'input_c': 10,
        'pred_file': 'pred.csv'
    }
    
    if os.path.exists(test_config['data_path']):
        run_visualization(**test_config)
    else:
        print(f"TEST dataset not found at {test_config['data_path']}")
    
    # Example 3: MSL dataset
    print("\nExample 3: MSL Dataset")
    print("-" * 30)
    msl_config = {
        'dataset': 'MSL',
        'data_path': 'dataset/MSL',
        'input_c': 55,
        'pred_file': 'pred.csv'
    }
    
    if os.path.exists(msl_config['data_path']):
        run_visualization(**msl_config)
    else:
        print(f"MSL dataset not found at {msl_config['data_path']}")
    
    # Example 4: SMAP dataset
    print("\nExample 4: SMAP Dataset")
    print("-" * 30)
    smap_config = {
        'dataset': 'SMAP',
        'data_path': 'dataset/SMAP',
        'input_c': 25,
        'pred_file': 'pred.csv'
    }
    
    if os.path.exists(smap_config['data_path']):
        run_visualization(**smap_config)
    else:
        print(f"SMAP dataset not found at {smap_config['data_path']}")
    
    print("\n" + "=" * 60)
    print("All visualization examples completed!")
    print("Generated files:")
    print("- {dataset}_anomaly_detection_figure9.png")
    print("- {dataset}_anomaly_detection_figure9.pdf")
    print("=" * 60)

def run_single_dataset():
    """
    Interactive mode to run visualization for a single dataset
    """
    print("\nInteractive Mode - Single Dataset Visualization")
    print("-" * 50)
    
    # Get user input
    dataset = input("Enter dataset name (SWaT, MSL, SMAP, SMD, PSM, TEST): ").strip()
    data_path = input("Enter data path (e.g., dataset/SWaT): ").strip()
    input_c = int(input("Enter number of input channels: ").strip())
    pred_file = input("Enter prediction file path (default: pred.csv): ").strip() or 'pred.csv'
    
    energy_file = input("Enter energy file path (optional, press Enter to skip): ").strip()
    energy_file = energy_file if energy_file else None
    
    # Run visualization
    run_visualization(dataset, data_path, input_c, pred_file, energy_file)

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--interactive':
        run_single_dataset()
    else:
        main()
