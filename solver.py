import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
import pandas as pd
import time
from utils.utils import *
from model.AnomalyTransformer import AnomalyTransformer
from data_factory.data_loader import get_loader_segment
import matplotlib.pyplot as plt

import seaborn as sns
from pyecharts import options as opts
from pyecharts.charts import HeatMap

try:
    from thop import profile
except ImportError:
    profile = None

def my_kl_loss(p, q):
    res = p * (torch.log(p + 0.0001) - torch.log(q + 0.0001))
    return torch.mean(torch.sum(res, dim=-1), dim=1)


def adjust_learning_rate(optimizer, epoch, lr_):
    lr_adjust = {epoch: lr_ * (0.5 ** ((epoch - 1) // 1))}
    if epoch in lr_adjust.keys():
        lr = lr_adjust[epoch]
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr
        print('Updating learning rate to {}'.format(lr))


class EarlyStopping:
    def __init__(self, patience=7, verbose=False, dataset_name='', delta=0):
        self.patience = patience
        self.verbose = verbose
        self.counter = 0
        self.best_score = None
        self.best_score2 = None
        self.early_stop = False
        self.val_loss_min = np.Inf
        self.val_loss2_min = np.Inf
        self.delta = delta
        self.dataset = dataset_name

    def __call__(self, val_loss, val_loss2, model, path):
        score = -val_loss
        score2 = -val_loss2
        if self.best_score is None:
            self.best_score = score
            self.best_score2 = score2
            self.save_checkpoint(val_loss, val_loss2, model, path)
        elif score < self.best_score + self.delta or score2 < self.best_score2 + self.delta:
            self.counter += 1
            print(f'EarlyStopping counter: {self.counter} out of {self.patience}')
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            self.best_score = score
            self.best_score2 = score2
            self.save_checkpoint(val_loss, val_loss2, model, path)
            self.counter = 0

    def save_checkpoint(self, val_loss, val_loss2, model, path):
        if self.verbose:
            print(f'Validation loss decreased ({self.val_loss_min:.6f} --> {val_loss:.6f}).  Saving model ...')
        torch.save(model.state_dict(), os.path.join(path, str(self.dataset) + '_checkpoint.pth'))
        self.val_loss_min = val_loss
        self.val_loss2_min = val_loss2


class Solver(object):
    DEFAULTS = {}

    def __init__(self, config):

        self.__dict__.update(Solver.DEFAULTS, **config)

        self.train_loader = get_loader_segment(self.data_path, batch_size=self.batch_size, win_size=self.win_size,
                                               mode='train',
                                               dataset=self.dataset)
        self.vali_loader = get_loader_segment(self.data_path, batch_size=self.batch_size, win_size=self.win_size,
                                              mode='val',
                                              dataset=self.dataset)
        self.test_loader = get_loader_segment(self.data_path, batch_size=self.batch_size, win_size=self.win_size,
                                              mode='test',
                                              dataset=self.dataset)
        self.thre_loader = get_loader_segment(self.data_path, batch_size=self.batch_size, win_size=self.win_size,
                                              mode='thre',
                                              dataset=self.dataset)

        self.build_model()
        self.device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        self.criterion = nn.MSELoss()

    def build_model(self):
        self.model = AnomalyTransformer(win_size=self.win_size, enc_in=self.input_c, c_out=self.output_c, e_layers=3)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.lr)

        if torch.cuda.is_available():
            self.model.cuda()

    def vali(self, vali_loader):
        self.model.eval()

        loss_1 = []
        loss_2 = []
        for i, (input_data, _) in enumerate(vali_loader):
            input = input_data.float().to(self.device)
            output, series, prior, _ = self.model(input)
            series_loss = 0.0
            prior_loss = 0.0
            for u in range(len(prior)):
                series_loss += (torch.mean(my_kl_loss(series[u], (
                        prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                               self.win_size)).detach())) + torch.mean(
                    my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)).detach(),
                        series[u])))
                prior_loss += (torch.mean(
                    my_kl_loss((prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                       self.win_size)),
                               series[u].detach())) + torch.mean(
                    my_kl_loss(series[u].detach(),
                               (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                       self.win_size)))))
            series_loss = series_loss / len(prior)
            prior_loss = prior_loss / len(prior)

            rec_loss = self.criterion(output, input)
            loss_1.append((rec_loss - self.k * series_loss).item())
            loss_2.append((rec_loss + self.k * prior_loss).item())

        return np.average(loss_1), np.average(loss_2)

    def train(self):

        print("======================TRAIN MODE======================")

        time_now = time.time()
        path = self.model_save_path
        if not os.path.exists(path):
            os.makedirs(path)
        early_stopping = EarlyStopping(patience=3, verbose=True, dataset_name=self.dataset)
        train_steps = len(self.train_loader)

        for epoch in range(self.num_epochs):
            iter_count = 0
            loss1_list = []

            epoch_time = time.time()
            self.model.train()
            for i, (input_data, labels) in enumerate(self.train_loader):

                self.optimizer.zero_grad()
                iter_count += 1
                input = input_data.float().to(self.device)

                output, series, prior, _ = self.model(input) #prior和series代表预测的注意力或概率分布。

                # calculate Association discrepancy
                series_loss = 0.0
                prior_loss = 0.0
                for u in range(len(prior)):
                    #计算关联差异 AssDis
                    series_loss += (torch.mean(my_kl_loss(series[u], (#series_loss 计算的是预测的 series 与归一化的 prior 之间的差异，以及相反方向的差异，这种双向 KL 散度有助于更好地捕捉分布差异。
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach())) + torch.mean(
                        my_kl_loss((prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                           self.win_size)).detach(),#用于在计算图中断开梯度传递，防止在反向传播时更新这些变量。
                                   series[u])))#计算 KL 散度（Kullback-Leibler divergence） 对 prior[u] 进行归一化处理，使其沿着最后一个维度的总和为1
                    #计算损失函数
                    prior_loss += (torch.mean(my_kl_loss(#计算的是归一化的 prior 和 series 之间的 KL 散度
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach())) + torch.mean(
                        my_kl_loss(series[u].detach(), (
                                prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                       self.win_size)))))
                series_loss = series_loss / len(prior)
                prior_loss = prior_loss / len(prior)

                rec_loss = self.criterion(output, input)
                with open('loss_log_399_d4.txt', 'a') as file:
                    formatted_loss = f'{rec_loss.item():.4f}'
                    # 写入文件
                    file.write(formatted_loss + '\n')

                loss1_list.append((rec_loss - self.k * series_loss).item())
                loss1 = rec_loss - self.k * series_loss #重构损失减去关联差异的加权项
                loss2 = rec_loss + self.k * prior_loss #重构损失加上先验损失的加权项

                if (i + 1) % 100 == 0:
                    speed = (time.time() - time_now) / iter_count
                    left_time = speed * ((self.num_epochs - epoch) * train_steps - i)
                    print('\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))
                    iter_count = 0
                    time_now = time.time()

                # Minimax strategy
                loss1.backward(retain_graph=True)
                loss2.backward()
                self.optimizer.step()

            print("Epoch: {} cost time: {}".format(epoch + 1, time.time() - epoch_time))
            train_loss = np.average(loss1_list)

            vali_loss1, vali_loss2 = self.vali(self.test_loader)

            print(
                "Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f} ".format(
                    epoch + 1, train_steps, train_loss, vali_loss1))
            early_stopping(vali_loss1, vali_loss2, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break
            adjust_learning_rate(self.optimizer, epoch + 1, self.lr)

    def test(self):
        self.model.load_state_dict(
            torch.load(
                os.path.join(str(self.model_save_path), str(self.dataset) + '_checkpoint.pth')))
        self.model.eval()
        temperature = 50

        print("======================TEST MODE======================")

        # ====== Part 1: Calculate Computational Cost Metrics ======
        print("\n--- Computational Cost Analysis ---")
        # 1. Model Parameters and Size
        num_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"Total trainable parameters: {num_params / 1e6:.2f}M")
        model_path = os.path.join(str(self.model_save_path), str(self.dataset) + '_checkpoint.pth')
        if os.path.exists(model_path):
            model_size = os.path.getsize(model_path) / (1024 * 1024)
            print(f"Model size on disk: {model_size:.2f} MB")

        # 2. FLOPs (Floating Point Operations)
        if profile:
            # Note: FLOPs calculation is input-dependent.
            dummy_input = torch.randn(1, self.win_size, self.input_c).to(self.device)
            macs, _ = profile(self.model, inputs=(dummy_input,), verbose=False)
            flops = macs * 2  # FLOPs are typically ~2 * MACs
            print(f"FLOPs per sample: {flops / 1e9:.2f} GFLOPs")
        else:
            print("FLOPs calculation skipped: 'thop' library not found. (pip install thop)")

        # Prepare for inference time and memory measurement
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats(self.device)

        print("-------------------------------------\n")

        criterion = nn.MSELoss(reduce=False)

        # (1) stastic on the train set
        attens_energy = []
        for i, (input_data, labels) in enumerate(self.train_loader):
            input = input_data.float().to(self.device)
            output, series, prior, _ = self.model(input)
            loss = torch.mean(criterion(input, output), dim=-1)
            series_loss = 0.0
            prior_loss = 0.0
            for u in range(len(prior)):
                if u == 0:
                    series_loss = my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss = my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature
                else:
                    series_loss += my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss += my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature

            metric = torch.softmax((-series_loss - prior_loss), dim=-1)
            cri = metric * loss
            cri = cri.detach().cpu().numpy()
            attens_energy.append(cri)

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        train_energy = np.array(attens_energy)

        # (2) find the threshold
        attens_energy = []
        for i, (input_data, labels) in enumerate(self.thre_loader):
            input = input_data.float().to(self.device)
            output, series, prior, _ = self.model(input)

            loss = torch.mean(criterion(input, output), dim=-1)

            series_loss = 0.0
            prior_loss = 0.0
            for u in range(len(prior)):
                if u == 0:
                    series_loss = my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss = my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature
                else:
                    series_loss += my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss += my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature
            # Metric
            metric = torch.softmax((-series_loss - prior_loss), dim=-1)
            cri = metric * loss
            cri = cri.detach().cpu().numpy()
            attens_energy.append(cri)

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        test_energy = np.array(attens_energy)
        combined_energy = np.concatenate([train_energy, test_energy], axis=0)
        thresh = np.percentile(combined_energy, 100 - self.anormly_ratio)
        print("Threshold :", thresh)

        # (3) evaluation on the test set
        test_labels = []
        attens_energy = []
        output_attention = []

        # ====== Part 2: Measure Inference Time and Memory ======
        inference_start_time = time.time()
        total_samples = 0

        for i, (input_data, labels) in enumerate(self.thre_loader):
            input = input_data.float().to(self.device)
            output, series, prior, _ = self.model(input)

            loss = torch.mean(criterion(input, output), dim=-1)
            # loss = np.mean(output.detach().cpu().numpy())
            output_attention.append(output.detach().cpu().numpy())

            #print(len(output_attention))
            series_loss = 0.0
            prior_loss = 0.0

            for u in range(len(prior)):
                if u == 0:
                    series_loss = my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss = my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature
                else:
                    series_loss += my_kl_loss(series[u], (
                            prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                   self.win_size)).detach()) * temperature
                    prior_loss += my_kl_loss(
                        (prior[u] / torch.unsqueeze(torch.sum(prior[u], dim=-1), dim=-1).repeat(1, 1, 1,
                                                                                                self.win_size)),
                        series[u].detach()) * temperature
            metric = torch.softmax((-series_loss - prior_loss), dim=-1)

            cri = metric * loss
            cri = cri.detach().cpu().numpy()
            attens_energy.append(cri)
            # attens_energy.append(loss)
            total_samples += input_data.shape[0]  # track total samples
            test_labels.append(labels)

        # --- End of Inference Measurement ---
        inference_total_time = time.time() - inference_start_time

        attens_energy = np.concatenate(attens_energy, axis=0).reshape(-1)
        test_labels = np.concatenate(test_labels, axis=0).reshape(-1)
        test_energy = np.array(attens_energy)
        test_labels = np.array(test_labels)

        pred = (test_energy > thresh).astype(int)

        gt = test_labels.astype(int)

        print("pred:   ", pred.shape)
        print("gt:     ", gt.shape)
        pred_tran = pd.DataFrame(pred)
        pred_tran.to_csv("pred.csv")

        # detection adjustment: please see this issue for more information https://github.com/thuml/Anomaly-Transformer/issues/14
        anomaly_state = False
        for i in range(len(gt)):
            if gt[i] == 1 and pred[i] == 1 and not anomaly_state:
                anomaly_state = True
                for j in range(i, 0, -1):
                    if gt[j] == 0:
                        break
                    else:
                        if pred[j] == 0:
                            pred[j] = 1
                for j in range(i, len(gt)):
                    if gt[j] == 0:
                        break
                    else:
                        if pred[j] == 0:
                            pred[j] = 1
            elif gt[i] == 0:
                anomaly_state = False
            if anomaly_state:
                pred[i] = 1

        pred = np.array(pred)
        gt = np.array(gt)
        plt.plot(pred)

        print("attens_energy: ",attens_energy.shape)
        #attens_energy_data = attens_energy.reshape(300, 400)
        #attens_energy_data = pd.DataFrame(attens_energy_data)

        last_dim = len(output_attention[0][0][0])
        print(last_dim)

        last_len = len(output_attention)-1
        print(last_len)

        last_final = (len(output_attention[-1]) * 100 + last_len * 6400)

        final_data = np.zeros((last_final, last_dim))
        # 处理前18组数据
        for i in range(last_len):
            data = output_attention[i]  # 获取第i组数据

            reshaped_data = data.reshape(-1, last_dim)  # 将数据重塑为 (115200, 18)
            final_data[i * 6400:(i + 1) * 6400, :] = reshaped_data  # 将数据放入最终数组

        # 处理第19组数据
        data_19 = output_attention[last_len]  # 获取第19组数据
        reshaped_data_19 = data_19.reshape(-1,last_dim)  # 将数据重塑为 (86400, 18)
        final_data[last_len * 6400:, :] = reshaped_data_19  # 将数据放入最终数组

        attens_energy_data = final_data
        df = pd.DataFrame(attens_energy_data)
        df.to_csv('attens_log_d4.csv', index=False, header=False)
        print("attens_energy_data: ",attens_energy_data.shape)
        sns.heatmap(attens_energy_data, vmin=0, vmax=0.5, cmap="YlGnBu", cbar=True)

        pred_atten = pd.DataFrame(np.mean(attens_energy_data, axis=1))
        pred_atten.to_csv("atten_d4.csv")

        # plt.figure()

        print("pred: ", pred.shape)
        # plt.plot(gt)
        print("gt:   ", gt.shape)
        # plt.show()

        from sklearn.metrics import precision_recall_fscore_support
        from sklearn.metrics import accuracy_score
        accuracy = accuracy_score(gt, pred)
        precision, recall, f_score, support = precision_recall_fscore_support(gt, pred,
                                                                              average='binary')
        # print(
        #     "Accuracy : {:0.4f}, Precision : {:0.4f}, Recall : {:0.4f}, F-score : {:0.4f} ".format(
        #         accuracy, precision,
        #         recall, f_score))
        print(f'Accuracy: {accuracy:.4f}')
        print(f'Precision: {precision:.4f}')
        print(f'Recall: {recall:.4f}')
        print(f'F1 Score: {f_score:.4f}')

        # ====== Part 3: Print Computational Cost Summary ======
        print("\n--- Computational Cost Summary ---")
        print(f"Total inference time for {total_samples} samples: {inference_total_time:.2f} seconds")
        if inference_total_time > 0:
            throughput = total_samples / inference_total_time
            print(f"Throughput: {throughput:.2f} samples/sec")
        if torch.cuda.is_available():
            peak_mem = torch.cuda.max_memory_allocated(self.device) / (1024 * 1024)
            print(f"Peak GPU Memory usage: {peak_mem:.2f} MB")
        print("Note: For CPU/GPU utilization and power consumption, please use external tools like 'htop' and 'nvidia-smi'.")
        print("----------------------------------\n")

        # ====== Part 4: Generate Figure 9 Style Visualization ======
        self.generate_anomaly_visualization(test_energy, gt, pred, thresh)

        pycharts_data = attens_energy_data.copy()

        print(pycharts_data.shape[0])
        print(pycharts_data.shape[1])

        # value = attens_energy_data
        # c = (
        #     HeatMap(init_opts=opts.InitOpts(height="900px", width='1500px'))
        #         .add_xaxis(list(range(pycharts_data.shape[1])))
        #         .add_yaxis("attens_energy_data", list(range(pycharts_data.shape[0])), value)
        #         .set_global_opts(
        #         title_opts=opts.TitleOpts(title="HeatMap-attention"),
        #         visualmap_opts=opts.VisualMapOpts(min_=0, max_=0.5),
        #         tooltip_opts=opts.TooltipOpts(axis_pointer_type="cross"),  # 指示器类型
        #         # toolbox_opts =opts.ToolboxOpts(is_show=True,orient='vertical'),
        #         # toolbox_opts=opts.ToolBoxFeatureDataZoomOpts(is_show=True),
        #         datazoom_opts=opts.DataZoomOpts(range_start=0, range_end=int(pycharts_data.shape[1] / 10), orient='vertical'),
        #         # 坐标轴进行缩放
        #     )
        #     .render("html/heatmap_base.html")
        # )

        return accuracy, precision, recall, f_score

    def generate_swat_visualization(self, test_energy, gt, pred, thresh):
        """
        Generate Figure 9(d) style visualization for SWaT dataset
        """
        print("\n=== Generating SWaT Visualization (Figure 9d style) ===")

        # Select a representative segment for visualization (similar to the paper)
        # Choose a segment that contains anomalies for better demonstration
        start_idx = 0
        end_idx = min(10000, len(test_energy))  # Show first 10000 points or all if less

        # Find a segment with anomalies for better visualization
        anomaly_indices = np.where(gt == 1)[0]
        if len(anomaly_indices) > 0:
            # Find a good segment around the first anomaly
            first_anomaly = anomaly_indices[0]
            start_idx = max(0, first_anomaly - 2000)
            end_idx = min(len(test_energy), first_anomaly + 8000)

        # Extract data for the selected segment
        segment_energy = test_energy[start_idx:end_idx]
        segment_gt = gt[start_idx:end_idx]
        segment_pred = pred[start_idx:end_idx]
        time_points = np.arange(len(segment_energy))

        # Load original test data for input time series visualization
        # We'll use the first dimension of the test data as representative
        try:
            test_data = np.load(self.data_path + "/swat_test.npy")
            # Select the same segment and use first dimension
            segment_input = test_data[start_idx:end_idx, 0]
        except:
            # If can't load original data, create a synthetic representation
            segment_input = np.sin(time_points * 0.01) + np.random.normal(0, 0.1, len(time_points))

        # Create the visualization
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        # Upper subplot: Input Time Series
        ax1.plot(time_points, segment_input, 'b-', linewidth=1, alpha=0.7, label='Input Time Series')

        # Highlight anomaly regions in upper plot
        anomaly_regions = []
        in_anomaly = False
        start_anomaly = None

        for i, is_anomaly in enumerate(segment_gt):
            if is_anomaly and not in_anomaly:
                start_anomaly = i
                in_anomaly = True
            elif not is_anomaly and in_anomaly:
                anomaly_regions.append((start_anomaly, i-1))
                in_anomaly = False

        if in_anomaly:  # Handle case where anomaly extends to the end
            anomaly_regions.append((start_anomaly, len(segment_gt)-1))

        # Add red shaded regions for anomalies in upper plot
        for start_anom, end_anom in anomaly_regions:
            ax1.axvspan(start_anom, end_anom, alpha=0.3, color='red', label='Anomaly' if start_anom == anomaly_regions[0][0] else "")

        ax1.set_ylabel('Input\nTime Series', fontsize=12)
        ax1.set_title('SWaT Dataset - Anomaly Detection Visualization', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right')

        # Lower subplot: Association-based Criterion
        ax2.plot(time_points, segment_energy, 'g-', linewidth=1, label='Association-based\nCriterion')

        # Add threshold line
        ax2.axhline(y=thresh, color='orange', linestyle='--', linewidth=2, label=f'Threshold ({thresh:.6f})')

        # Highlight predicted anomalies
        pred_anomaly_regions = []
        in_pred_anomaly = False
        start_pred_anomaly = None

        for i, is_pred_anomaly in enumerate(segment_pred):
            if is_pred_anomaly and not in_pred_anomaly:
                start_pred_anomaly = i
                in_pred_anomaly = True
            elif not is_pred_anomaly and in_pred_anomaly:
                pred_anomaly_regions.append((start_pred_anomaly, i-1))
                in_pred_anomaly = False

        if in_pred_anomaly:
            pred_anomaly_regions.append((start_pred_anomaly, len(segment_pred)-1))

        # Add red shaded regions for predicted anomalies
        for start_pred, end_pred in pred_anomaly_regions:
            ax2.axvspan(start_pred, end_pred, alpha=0.3, color='red', label='Detected Anomaly' if start_pred == pred_anomaly_regions[0][0] else "")

        # Add "Early Stage Detection" annotation if there are early detections
        if len(pred_anomaly_regions) > 0:
            # Find the first detection
            first_detection = pred_anomaly_regions[0][0]
            detection_value = segment_energy[first_detection]
            ax2.annotate('Early Stage\nDetection',
                        xy=(first_detection, detection_value),
                        xytext=(first_detection + 1000, detection_value + 0.02),
                        arrowprops=dict(arrowstyle='->', color='red', lw=2),
                        fontsize=12, color='red', fontweight='bold',
                        ha='center')

        ax2.set_ylabel('Association-based\nCriterion', fontsize=12)
        ax2.set_xlabel('Time', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper right')

        # Adjust layout and save
        plt.tight_layout()
        plt.savefig('swat_anomaly_detection_figure9d.png', dpi=300, bbox_inches='tight')
        plt.savefig('swat_anomaly_detection_figure9d.pdf', bbox_inches='tight')

        print(f"Visualization saved as 'swat_anomaly_detection_figure9d.png' and 'swat_anomaly_detection_figure9d.pdf'")
        print(f"Visualized segment: {start_idx} to {end_idx} (length: {end_idx - start_idx})")
        print(f"Number of anomalies in segment: {np.sum(segment_gt)}")
        print(f"Number of detected anomalies in segment: {np.sum(segment_pred)}")

        plt.show()

        return fig