#!/usr/bin/env python3
"""
Demo script showing different visualization modes for anomaly detection
"""

import subprocess
import sys
import os

def run_command(cmd):
    """Run a command and print the output"""
    print(f"\n{'='*80}")
    print(f"Running: {' '.join(cmd)}")
    print('='*80)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Success!")
            if result.stdout:
                print("Output:")
                print(result.stdout)
        else:
            print("❌ Error!")
            if result.stderr:
                print("Error output:")
                print(result.stderr)
    except Exception as e:
        print(f"❌ Failed to run command: {e}")

def demo_swat_visualizations():
    """Demo different visualization modes for SWaT dataset"""
    print("\n" + "="*80)
    print("DEMO: SWaT Dataset Visualization Modes")
    print("="*80)
    
    dataset_config = {
        'dataset': 'SWaT',
        'data_path': 'dataset/SWaT',
        'input_c': '51',
        'pred_file': 'pred.csv'
    }
    
    base_cmd = [
        'python', 'anomaly_visualization.py',
        '--dataset', dataset_config['dataset'],
        '--data_path', dataset_config['data_path'],
        '--input_c', dataset_config['input_c'],
        '--pred_file', dataset_config['pred_file']
    ]
    
    # Check if dataset exists
    if not os.path.exists(dataset_config['data_path']):
        print(f"❌ SWaT dataset not found at {dataset_config['data_path']}")
        return
    
    # 1. Auto mode (smart segment selection)
    print("\n1. AUTO MODE - Smart segment selection with anomalies")
    cmd_auto = base_cmd + ['--mode', 'auto']
    run_command(cmd_auto)
    
    # 2. Custom mode - first 10k points
    print("\n2. CUSTOM MODE - First 10,000 points")
    cmd_custom_10k = base_cmd + ['--mode', 'custom', '--start_idx', '0', '--end_idx', '10000']
    run_command(cmd_custom_10k)
    
    # 3. Custom mode - middle section
    print("\n3. CUSTOM MODE - Middle section (100k-110k)")
    cmd_custom_mid = base_cmd + ['--mode', 'custom', '--start_idx', '100000', '--end_idx', '110000']
    run_command(cmd_custom_mid)
    
    # 4. Full mode (if dataset is not too large)
    print("\n4. FULL MODE - Complete dataset (if not too large)")
    cmd_full = base_cmd + ['--mode', 'full']
    run_command(cmd_full)

def demo_test_visualizations():
    """Demo different visualization modes for TEST dataset"""
    print("\n" + "="*80)
    print("DEMO: TEST Dataset Visualization Modes")
    print("="*80)
    
    dataset_config = {
        'dataset': 'TEST',
        'data_path': 'dataset/TEST',
        'input_c': '10',
        'pred_file': 'pred.csv'
    }
    
    base_cmd = [
        'python', 'anomaly_visualization.py',
        '--dataset', dataset_config['dataset'],
        '--data_path', dataset_config['data_path'],
        '--input_c', dataset_config['input_c'],
        '--pred_file', dataset_config['pred_file']
    ]
    
    # Check if dataset exists
    if not os.path.exists(dataset_config['data_path']):
        print(f"❌ TEST dataset not found at {dataset_config['data_path']}")
        print("Creating synthetic test data...")
        # Create test data using the test script
        try:
            subprocess.run(['python', 'test_visualization.py'], check=True)
        except:
            print("❌ Failed to create test data")
            return
    
    # 1. Auto mode
    print("\n1. AUTO MODE - Smart segment selection")
    cmd_auto = base_cmd + ['--mode', 'auto']
    run_command(cmd_auto)
    
    # 2. Full mode
    print("\n2. FULL MODE - Complete dataset")
    cmd_full = base_cmd + ['--mode', 'full']
    run_command(cmd_full)
    
    # 3. Custom mode - specific anomaly region
    print("\n3. CUSTOM MODE - Focus on specific region (1000-3000)")
    cmd_custom = base_cmd + ['--mode', 'custom', '--start_idx', '1000', '--end_idx', '3000']
    run_command(cmd_custom)

def demo_integrated_solver():
    """Demo the integrated visualization in the main solver"""
    print("\n" + "="*80)
    print("DEMO: Integrated Visualization with Main Solver")
    print("="*80)
    
    print("The modified solver.py now automatically generates multiple visualizations:")
    print("1. Full dataset (if <= 50k points)")
    print("2. Auto-selected representative segment")
    print("3. First 10k points (for comparison)")
    print("\nTo test this, run your original commands:")
    
    commands = [
        "python main.py --anormly_ratio 0.1 --num_epochs 10 --batch_size 64 --mode test --dataset SWaT --data_path dataset/SWaT --input_c 51 --output_c 51 --pretrained_model 10",
        "python main.py --anormly_ratio 1 --num_epochs 10 --batch_size 64 --mode test --dataset TEST --data_path dataset/TEST --input_c 10 --output_c 10 --pretrained_model 20"
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"\n{i}. {cmd}")

def show_generated_files():
    """Show what files are generated"""
    print("\n" + "="*80)
    print("GENERATED FILES")
    print("="*80)
    
    print("After running the visualizations, you'll get files like:")
    print("\nFor different modes:")
    print("- {dataset}_anomaly_detection_figure9_auto.png/pdf")
    print("- {dataset}_anomaly_detection_figure9_full.png/pdf")
    print("- {dataset}_anomaly_detection_figure9_custom_{start}_{end}.png/pdf")
    
    print("\nFor example, SWaT dataset might generate:")
    print("- swat_anomaly_detection_figure9_auto.png")
    print("- swat_anomaly_detection_figure9_full.png")
    print("- swat_anomaly_detection_figure9_custom_0_10000.png")
    
    print("\nFor TEST dataset:")
    print("- test_anomaly_detection_figure9_auto.png")
    print("- test_anomaly_detection_figure9_full.png")
    print("- test_anomaly_detection_figure9_custom_1000_3000.png")

def main():
    """Main demo function"""
    print("UNIVERSAL ANOMALY DETECTION VISUALIZATION DEMO")
    print("="*80)
    print("This demo shows how to use different visualization modes:")
    print("1. AUTO: Automatically selects segments with anomalies")
    print("2. FULL: Shows the complete dataset")
    print("3. CUSTOM: Shows user-specified range")
    print("="*80)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == 'swat':
            demo_swat_visualizations()
        elif mode == 'test':
            demo_test_visualizations()
        elif mode == 'solver':
            demo_integrated_solver()
        elif mode == 'files':
            show_generated_files()
        else:
            print(f"Unknown mode: {mode}")
            print("Available modes: swat, test, solver, files")
    else:
        print("\nUsage:")
        print("python demo_visualization_modes.py [mode]")
        print("\nAvailable modes:")
        print("- swat: Demo SWaT dataset visualizations")
        print("- test: Demo TEST dataset visualizations")
        print("- solver: Show integrated solver usage")
        print("- files: Show generated file examples")
        print("\nOr run without arguments to see this help.")
        
        print("\n" + "="*80)
        print("QUICK START EXAMPLES:")
        print("="*80)
        
        print("\n1. For your SWaT dataset:")
        print("python anomaly_visualization.py --dataset SWaT --data_path dataset/SWaT --input_c 51 --mode full")
        
        print("\n2. For your TEST dataset:")
        print("python anomaly_visualization.py --dataset TEST --data_path dataset/TEST --input_c 10 --mode auto")
        
        print("\n3. Custom range:")
        print("python anomaly_visualization.py --dataset SWaT --data_path dataset/SWaT --input_c 51 --mode custom --start_idx 0 --end_idx 50000")

if __name__ == '__main__':
    main()
