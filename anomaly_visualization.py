import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import os
from sklearn.preprocessing import StandardScaler


class UniversalAnomalyVisualizer:
    """
    Universal anomaly detection visualization tool for different datasets
    """
    
    def __init__(self, dataset_name, data_path, input_c):
        self.dataset = dataset_name
        self.data_path = data_path
        self.input_c = input_c
        self.scaler = StandardScaler()
        
    def load_test_data(self):
        """
        Load test data based on dataset type
        """
        try:
            if self.dataset == 'SWaT':
                test_data = np.load(self.data_path + "/swat_test.npy")
                test_labels = np.load(self.data_path + "/swat_test_label.npy")
            elif self.dataset == 'MSL':
                test_data = np.load(self.data_path + "/MSL_test.npy")
                test_labels = np.load(self.data_path + "/MSL_test_label.npy")
            elif self.dataset == 'SMAP':
                test_data = np.load(self.data_path + "/SMAP_test.npy")
                test_labels = np.load(self.data_path + "/SMAP_test_label.npy")
            elif self.dataset == 'SMD':
                test_data = np.load(self.data_path + "/SMD_test.npy")
                test_labels = np.load(self.data_path + "/SMD_test_label.npy")
            elif self.dataset == 'PSM':
                test_data = pd.read_csv(self.data_path + '/test.csv')
                test_data = test_data.values[:, 1:]
                test_labels = pd.read_csv(self.data_path + '/test_label.csv').values[:, 1:]
            elif self.dataset == 'TEST':
                test_data = pd.read_csv(self.data_path + '/test.csv')
                test_data = test_data.values[:, 1:]
                test_labels = pd.read_csv(self.data_path + '/test_label.csv').values[:, 1:]
            else:
                # Generic fallback
                try:
                    test_data = np.load(self.data_path + f"/{self.dataset.lower()}_test.npy")
                    test_labels = np.load(self.data_path + f"/{self.dataset.lower()}_test_label.npy")
                except:
                    test_data = pd.read_csv(self.data_path + '/test.csv')
                    test_data = test_data.values[:, 1:]
                    test_labels = pd.read_csv(self.data_path + '/test_label.csv').values[:, 1:]
            
            return test_data, test_labels.flatten()
            
        except Exception as e:
            raise FileNotFoundError(f"Could not load test data for dataset {self.dataset}: {e}")
    
    def generate_visualization(self, test_energy=None, gt=None, pred=None, thresh=None, 
                             pred_file='pred.csv', energy_file=None):
        """
        Generate Figure 9 style visualization
        """
        print(f"\n=== Generating {self.dataset} Visualization (Figure 9 style) ===")
        
        # Load data if not provided
        if test_energy is None or gt is None:
            try:
                test_data, gt = self.load_test_data()
                print(f"Loaded test data shape: {test_data.shape}")
                print(f"Loaded labels shape: {gt.shape}")
            except Exception as e:
                print(f"Error loading test data: {e}")
                return None
        
        # Load predictions if not provided
        if pred is None:
            try:
                pred_df = pd.read_csv(pred_file)
                pred = pred_df.values.flatten()
                print(f"Loaded predictions from {pred_file}, shape: {pred.shape}")
            except Exception as e:
                print(f"Warning: Could not load predictions from {pred_file}: {e}")
                pred = np.zeros_like(gt)
        
        # Load energy scores if not provided
        if test_energy is None:
            if energy_file and os.path.exists(energy_file):
                try:
                    energy_df = pd.read_csv(energy_file)
                    test_energy = energy_df.values.flatten()
                    print(f"Loaded energy scores from {energy_file}, shape: {test_energy.shape}")
                except Exception as e:
                    print(f"Warning: Could not load energy scores: {e}")
                    test_energy = np.random.random(len(gt))
            else:
                print("Warning: No energy scores provided, using random values for demonstration")
                test_energy = np.random.random(len(gt))
        
        # Set threshold if not provided
        if thresh is None:
            thresh = np.percentile(test_energy, 90)  # Use 90th percentile as default
            print(f"Using default threshold: {thresh}")
        
        # Ensure all arrays have the same length
        min_length = min(len(test_energy), len(gt), len(pred))
        test_energy = test_energy[:min_length]
        gt = gt[:min_length]
        pred = pred[:min_length]
        
        # Select a representative segment for visualization
        start_idx = 0
        end_idx = min(10000, len(test_energy))
        
        # Find a segment with anomalies for better visualization
        anomaly_indices = np.where(gt == 1)[0]
        if len(anomaly_indices) > 0:
            first_anomaly = anomaly_indices[0]
            start_idx = max(0, first_anomaly - 2000)
            end_idx = min(len(test_energy), first_anomaly + 8000)
        
        # Extract data for the selected segment
        segment_energy = test_energy[start_idx:end_idx]
        segment_gt = gt[start_idx:end_idx]
        segment_pred = pred[start_idx:end_idx]
        time_points = np.arange(len(segment_energy))
        
        # Load original test data for input time series visualization
        try:
            test_data, _ = self.load_test_data()
            segment_input = test_data[start_idx:end_idx, 0]
        except:
            # Create synthetic data if original data can't be loaded
            segment_input = np.sin(time_points * 0.01) + np.random.normal(0, 0.1, len(time_points))
        
        # Create the visualization
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # Upper subplot: Input Time Series
        ax1.plot(time_points, segment_input, 'b-', linewidth=1, alpha=0.7, label='Input Time Series')
        
        # Highlight anomaly regions in upper plot
        self._add_anomaly_regions(ax1, segment_gt, time_points, 'Anomaly')
        
        ax1.set_ylabel('Input\nTime Series', fontsize=12)
        ax1.set_title(f'{self.dataset} Dataset - Anomaly Detection Visualization', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right')
        
        # Lower subplot: Association-based Criterion
        ax2.plot(time_points, segment_energy, 'g-', linewidth=1, label='Association-based\nCriterion')
        
        # Add threshold line
        ax2.axhline(y=thresh, color='orange', linestyle='--', linewidth=2, label=f'Threshold ({thresh:.6f})')
        
        # Highlight predicted anomalies
        self._add_anomaly_regions(ax2, segment_pred, time_points, 'Detected Anomaly')
        
        # Add "Early Stage Detection" annotation if there are early detections
        pred_anomaly_indices = np.where(segment_pred == 1)[0]
        if len(pred_anomaly_indices) > 0:
            first_detection = pred_anomaly_indices[0]
            detection_value = segment_energy[first_detection]
            ax2.annotate('Early Stage\nDetection', 
                        xy=(first_detection, detection_value), 
                        xytext=(first_detection + 1000, detection_value + 0.02),
                        arrowprops=dict(arrowstyle='->', color='red', lw=2),
                        fontsize=12, color='red', fontweight='bold',
                        ha='center')
        
        ax2.set_ylabel('Association-based\nCriterion', fontsize=12)
        ax2.set_xlabel('Time', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper right')
        
        # Adjust layout and save
        plt.tight_layout()
        
        # Generate filename based on dataset name
        filename_base = f'{self.dataset.lower()}_anomaly_detection_figure9'
        plt.savefig(f'{filename_base}.png', dpi=300, bbox_inches='tight')
        plt.savefig(f'{filename_base}.pdf', bbox_inches='tight')
        
        print(f"Visualization saved as '{filename_base}.png' and '{filename_base}.pdf'")
        print(f"Visualized segment: {start_idx} to {end_idx} (length: {end_idx - start_idx})")
        print(f"Number of anomalies in segment: {np.sum(segment_gt)}")
        print(f"Number of detected anomalies in segment: {np.sum(segment_pred)}")
        
        plt.show()
        
        return fig
    
    def _add_anomaly_regions(self, ax, anomaly_labels, time_points, label):
        """
        Add shaded regions for anomalies
        """
        anomaly_regions = []
        in_anomaly = False
        start_anomaly = None
        
        for i, is_anomaly in enumerate(anomaly_labels):
            if is_anomaly and not in_anomaly:
                start_anomaly = i
                in_anomaly = True
            elif not is_anomaly and in_anomaly:
                anomaly_regions.append((start_anomaly, i-1))
                in_anomaly = False
        
        if in_anomaly:
            anomaly_regions.append((start_anomaly, len(anomaly_labels)-1))
        
        # Add red shaded regions for anomalies
        for i, (start_anom, end_anom) in enumerate(anomaly_regions):
            ax.axvspan(start_anom, end_anom, alpha=0.3, color='red', 
                      label=label if i == 0 else "")


def main():
    parser = argparse.ArgumentParser(description='Universal Anomaly Detection Visualization')
    parser.add_argument('--dataset', type=str, required=True, help='Dataset name (SWaT, MSL, SMAP, SMD, PSM, TEST)')
    parser.add_argument('--data_path', type=str, required=True, help='Path to dataset')
    parser.add_argument('--input_c', type=int, required=True, help='Number of input channels')
    parser.add_argument('--pred_file', type=str, default='pred.csv', help='Path to predictions file')
    parser.add_argument('--energy_file', type=str, default=None, help='Path to energy scores file')
    
    args = parser.parse_args()
    
    # Create visualizer
    visualizer = UniversalAnomalyVisualizer(args.dataset, args.data_path, args.input_c)
    
    # Generate visualization
    visualizer.generate_visualization(pred_file=args.pred_file, energy_file=args.energy_file)


if __name__ == '__main__':
    main()
