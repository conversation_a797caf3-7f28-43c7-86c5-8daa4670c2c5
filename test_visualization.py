#!/usr/bin/env python3
"""
Test script for the universal anomaly visualization tool
"""

import numpy as np
import pandas as pd
import os
from anomaly_visualization import UniversalAnomalyVisualizer

def create_test_data():
    """
    Create synthetic test data for demonstration
    """
    print("Creating synthetic test data for demonstration...")
    
    # Create test directory
    test_dir = "dataset/TEST"
    os.makedirs(test_dir, exist_ok=True)
    
    # Generate synthetic time series data
    n_samples = 5000
    n_features = 10
    
    # Create normal data with some patterns
    time = np.arange(n_samples)
    data = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        # Different patterns for different features
        data[:, i] = (
            np.sin(time * 0.01 * (i + 1)) + 
            0.5 * np.cos(time * 0.005 * (i + 1)) + 
            np.random.normal(0, 0.1, n_samples)
        )
    
    # Add anomalies
    anomaly_regions = [
        (1000, 1200),  # First anomaly
        (2500, 2800),  # Second anomaly
        (4000, 4100),  # Third anomaly
    ]
    
    labels = np.zeros(n_samples)
    
    for start, end in anomaly_regions:
        # Add anomalous patterns
        for i in range(n_features):
            data[start:end, i] += np.random.normal(2, 0.5, end - start)
        labels[start:end] = 1
    
    # Save test data
    test_df = pd.DataFrame(data)
    test_df.insert(0, 'timestamp', time)  # Add timestamp column
    test_df.to_csv(os.path.join(test_dir, 'test.csv'), index=False)
    
    # Save labels
    label_df = pd.DataFrame(labels.reshape(-1, 1))
    label_df.insert(0, 'timestamp', time)
    label_df.to_csv(os.path.join(test_dir, 'test_label.csv'), index=False)
    
    # Create synthetic predictions (with some detection errors)
    pred = np.zeros(n_samples)
    for start, end in anomaly_regions:
        # Simulate early detection and some false negatives
        pred_start = max(0, start - 50)  # Early detection
        pred_end = min(n_samples, end + 20)  # Slightly extended detection
        pred[pred_start:pred_end] = 1
    
    # Add some false positives
    false_positive_regions = [(500, 520), (3000, 3030)]
    for start, end in false_positive_regions:
        pred[start:end] = 1
    
    # Save predictions
    pred_df = pd.DataFrame(pred.reshape(-1, 1))
    pred_df.to_csv('pred.csv', index=False, header=False)
    
    # Create synthetic energy scores
    energy = np.random.random(n_samples) * 0.5
    for start, end in anomaly_regions:
        energy[start:end] += np.random.random(end - start) * 1.5  # Higher energy for anomalies
    
    energy_df = pd.DataFrame(energy.reshape(-1, 1))
    energy_df.to_csv('energy_scores.csv', index=False, header=False)
    
    print(f"Created synthetic data:")
    print(f"- Test data: {test_df.shape}")
    print(f"- Labels: {np.sum(labels)} anomalies out of {len(labels)} samples")
    print(f"- Predictions: {np.sum(pred)} detected anomalies")
    print(f"- Files saved in {test_dir}/")
    
    return test_dir

def test_visualization():
    """
    Test the visualization tool
    """
    print("Testing Universal Anomaly Visualization Tool")
    print("=" * 50)
    
    # Create test data
    test_dir = create_test_data()
    
    # Test the visualizer
    try:
        visualizer = UniversalAnomalyVisualizer(
            dataset_name='TEST',
            data_path=test_dir,
            input_c=10
        )
        
        # Generate visualization
        fig = visualizer.generate_visualization(
            pred_file='pred.csv',
            energy_file='energy_scores.csv'
        )
        
        if fig is not None:
            print("\n✅ Visualization generated successfully!")
            print("Check the generated files:")
            print("- test_anomaly_detection_figure9.png")
            print("- test_anomaly_detection_figure9.pdf")
        else:
            print("\n❌ Visualization failed!")
            
    except Exception as e:
        print(f"\n❌ Error during visualization: {e}")
        import traceback
        traceback.print_exc()

def test_different_datasets():
    """
    Test visualization with different dataset configurations
    """
    print("\nTesting different dataset configurations...")
    
    # Test configurations
    configs = [
        {
            'name': 'TEST_Small',
            'dataset': 'TEST',
            'data_path': 'dataset/TEST',
            'input_c': 10,
            'description': 'Custom TEST dataset with 10 features'
        },
        # Add more configurations as needed
    ]
    
    for config in configs:
        print(f"\nTesting {config['name']}: {config['description']}")
        try:
            visualizer = UniversalAnomalyVisualizer(
                dataset_name=config['dataset'],
                data_path=config['data_path'],
                input_c=config['input_c']
            )
            
            fig = visualizer.generate_visualization()
            if fig is not None:
                print(f"✅ {config['name']} visualization successful")
            else:
                print(f"❌ {config['name']} visualization failed")
                
        except Exception as e:
            print(f"❌ {config['name']} error: {e}")

if __name__ == '__main__':
    test_visualization()
    test_different_datasets()
    
    print("\n" + "=" * 50)
    print("Testing completed!")
    print("=" * 50)
